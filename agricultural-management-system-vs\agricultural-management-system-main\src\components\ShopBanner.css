

/* 🌟 Shop Banner */
.shop-banner {
  position: relative;
  width: 100vw;
  min-height: 50vh;
  background: url("../assets/images/shop-detail.jpg") center/cover no-repeat fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* 🔥 Overlay Effect */
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3); /* Black overlay with 50% opacity */
  z-index: 1; /* Keeps overlay above background */
}

/* 🌟 Content Styling */
.content {
  position: relative;
  z-index: 2; /* Ensures text appears above the overlay */
  color: white;
}

/* 🌟 Subheading */
.subheading {
  color: #f3c258; /* Golden color */
  font-size: 20px;
  font-weight: bold;
}

/* 🌟 Main Title */
.title {
  font-size: 50px;
  font-weight: bold;
  font-family: "Poppins", sans-serif;
  text-transform: uppercase;
}

/* 🌟 Yellow Underline */
.underline {
  width: 100px;
  height: 5px;
  background-color: #f3c258;
  margin: 10px auto;
}

/* 🌟 Breadcrumb Navigation */
.breadcrumb-container {
  font-size: 18px;
  font-weight: bold;
}
/* 🌟 Breadcrumb Navigation Links */
.breadcrumb-link {
  color: white;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s;
}

.breadcrumb-link:hover {
  color: #18a728; /* Golden color on hover */
}
