{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
    
      "AgroMind.GP.Service.Services": "Information", // To see logs from TokenService
      "AgroMind.GP.APIs": "Information" // To see logs from IdentityServiceExtension
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DefaultConnection": "Server=.;Database=AgroMindDB;Trusted_Connection=True;MultipleActiveResultSets=true;TrustServerCertificate=True",
    "RedisConnection": "localhost"
  },
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "Port": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "ciduhpxgftiqfedf"
  },
  "JWT": {
    "key": "SuperSecureAuthenticationKey123456789!",
    "ValidIssuer": "https://localhost:7057",
    //"ValidAudience": "MyS<PERSON>ureKey",
    "ValidAudience": "MySecureClientApp", // Make this more specific, the URL of  frontend 
    "DurationInDays": "4"
  }
}